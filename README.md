# Flow API 
Difinity flow is the main heart of the platform. This provides a unified REST api for all your AI workloads and takes all the work away to integrate with several AI models and make sure all those integrations are fully compliant. 


## Application Architecture
The hexagonal architecture, or ports and adapters architecture, is an architectural pattern used in software design. It aims at creating loosely coupled application components that can be easily connected to their software environment by means of ports and adapters. This makes components exchangeable at any level and facilitates test automation. Read more about it [here](https://medium.com/the-software-architecture-chronicles/ports-adapters-architecture-d19f2d476eca).

![img.png](ports-and-adapters.png)

In hexagonal architecture, the dependencies always flow inwards towards the core domain. The core domain is the most important part of the application and it should not depend on any other part of the application. The core domain should be independent of the database, the UI, and any other external element. The core domain should be the most stable part of the application. The core domain must never depend on anything else and everything else in the application must depend on the core. 

### Incoming (rest package)
 - This is the entry point of the application. 
 - It is responsible for receiving the input from the user and passing it to the core domain. 
 - The incoming layer is the only layer that can depend on external libraries. 
 - The incoming layer is responsible for translating the input into a format that the core domain can understand. 
 - The incoming layer is also responsible for translating the output of the core domain into a format that the user can understand


### Core
- Contains all the business logic of the application.
- Contains the workflow of the application.
- Relies on abstractions but not concrete implementations.

To understand this concept, take a look at the AIModelProcessor interface and how it used in ChatService. The ChatService does not know the concrete implementation of the AIModelProcessor. It only knows that it can call the process method on the AIModelProcessor. This is an example of dependency inversion. The ChatService depends on an abstraction, not a concrete implementation. This makes the ChatService more flexible and easier to test.

### Outgoing
- The outgoing layer is responsible for interacting with external systems.
- The outgoing layer is responsible for translating the output of the core domain into a format that the external system can understand.
- The outgoing layer is also responsible for translating the input from the external system into a format that the core domain can understand.
- The outgoing layer in a lot of cases implements interfaces exposed by the core. To understand this, take a look at `ai.difinity.flow.adapters.openai.OpenAIProcessor` and how it implements the `AIModelProcessor` interface.




## Tech Stack & Setup Guide

### Technology Stack
- **Kotlin** - Primary programming language
- **Spring Boot 3.4.3** - Application framework
- **Java 21** - JVM version
- **Gradle** - Build tool
- **ArchUnit** - Architecture testing
- **JUnit 5** - Unit testing framework

### Prerequisites
- SDKMAN! (for managing Java versions)
- IntelliJ IDEA (recommended)
- Gradle 8.x+

### Local Setup

1. Install SDKMAN! (if not installed):
```bash
curl -s "https://get.sdkman.io" | bash
source "$HOME/.sdkman/bin/sdkman-init.sh"
```

2. Install Java 21:
```bash
sdk install java 21-tem
sdk use java 21-tem
```

3. Clone the repository:
```bash
git clone https://github.com/difinity-ai/flow.git
cd flow
```

4. Build the project:
```bash
./gradlew build
```

5. Run the application:
```bash
./gradlew bootRun
```

The application will start on `http://localhost:8080`

### API Endpoints

Base URL: `/api/v1`

Chat endpoint:
- POST `/chat`
- Content-Type: `application/json`
- Sample request:
```json
{
  "provider": "OPENAI",
  "model": "gpt-4",
  "messages": [
    {
      "role": "USER",
      "content": "Hello",
      "contentType": "TEXT"
    }
  ],
  "parameters": {
    "temperature": 0.7,
    "maxTokens": 1000
  }
}
```

### Development

- Use IntelliJ IDEA for the best development experience
- Enable Kotlin formatting in IntelliJ
- Run tests: `./gradlew test`
- Check architecture rules: `./gradlew archTest`


### Gradle Tasks
The following Gradle tasks are available for building Docker images and managing Docker containers:
- `./gradlew buildDockerImage`: Builds a Docker image using the existing Dockerfile.
- `./gradlew runDockerContainer`: Runs the Docker container from the built image.
- `./gradlew stopDockerContainer`: Stops and removes the Docker container.

## Model Management

### AWS CLI Setup

1. Install AWS CLI:
   
   **For macOS (using Homebrew):**
   ```bash
   brew install awscli
   ```
   
   **For Ubuntu/Debian:**
   ```bash
   sudo apt-get update
   sudo apt-get install awscli
   ```
   
   **For Windows:**
   Download and run the official AWS CLI MSI installer from the [AWS website](https://aws.amazon.com/cli/)

2. Configure AWS CLI:
   ```bash
   aws configure
   ```
   
   You'll be prompted for:
   - AWS Access Key ID
   - AWS Secret Access Key
   - Default region name (e.g., us-west-2)
   - Default output format (json recommended)

3. Verify installation:
   ```bash
   aws --version
   aws sts get-caller-identity
   ```

### Environment Variables

Set up required environment variables:

```bash
# Required for model management scripts
export AWS_ACCOUNT_ID=your_account_id
export AWS_REGION=your_region

# Optional: Override AWS CLI configuration
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
```

For persistent configuration, add these to your shell profile (`~/.bashrc`, `~/.zshrc`, etc.).

### Local Development

To work with models locally:

1. Download models from S3:
```bash
export AWS_ACCOUNT_ID=your_account_id
export AWS_REGION=your_region
./scripts/download_models_from_s3.sh
```

2. Make changes to models locally

3. Upload updated models to S3:
```bash
./scripts/upload_models_to_s3.sh
```

The upload script will:
- Archive existing models with timestamp
- Upload your local changes to the latest directory
- Maintain version history

Models are stored in:
- Latest: `s3://difinity-flow-models-{AWS_ACCOUNT_ID}/models/routellm-bert/latest/`
- Versions: `s3://difinity-flow-models-{AWS_ACCOUNT_ID}/models/routellm-bert/versions/{TIMESTAMP}/`


## Security Configuration

### Key Management Service (KMS)
The application uses AWS KMS for encryption at rest and key management. For detailed setup and configuration of KMS keys, refer to [KMS Setup Documentation](docs/kms-setup.md).

Key security features:
- Automatic key rotation
- Encrypted Lambda environment variables
- Encrypted API Gateway logs
- IAM role-based access control