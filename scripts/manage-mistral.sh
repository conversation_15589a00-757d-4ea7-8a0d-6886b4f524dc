#!/bin/bash

# This script manages Mistral-Nemo-Instruct-2407 models from Hugging Face
# It can download specific versions and upload them to cloud storage
# Usage:
#   --download --version=latest : Downloads the latest version of Mistral-Nemo-Instruct
#   --download --version=x.x    : Downloads a specific version of Mistral-Nemo-Instruct
#   --update --version=x.x      : Downloads and uploads a specific version to cloud storage

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default values
ACTION=""
VERSION=""
CLOUD_PROVIDER=${CLOUD_PROVIDER:-"aws"}
MODELS_DIR="../models/mistral-nemo"
MODELS_PATH="models/mistral-nemo"
MODEL_NAME="bartowski/Mistral-Nemo-Instruct-2407-GGUF"

# AWS-specific defaults
AWS_REGION=${AWS_REGION:-"ap-southeast-2"}
AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID:-""}
BUCKET_NAME=${MODELS_BUCKET_NAME:-"difinity-flow-models-$AWS_ACCOUNT_ID"}

# Azure-specific defaults
AZURE_STORAGE_ACCOUNT=${AZURE_STORAGE_ACCOUNT:-""}
AZURE_CONTAINER_NAME=${AZURE_CONTAINER_NAME:-"models"}

# GCP-specific defaults
GCP_PROJECT_ID=${GCP_PROJECT_ID:-""}
GCP_BUCKET_NAME=${GCP_BUCKET_NAME:-""}

# Function to display context-specific help information
show_help() {
    local context=$1
    local provider=$2

    echo -e "${GREEN}Mistral-Nemo-Instruct Model Management Script${NC}"

    # README section if explicitly requested
    if [ "$context" = "readme" ]; then
        echo -e "\n${GREEN}┌───────────────────────────────────────────────────────────────┐${NC}"
        echo -e "${GREEN}│                     DETAILED README                          │${NC}"
        echo -e "${GREEN}└───────────────────────────────────────────────────────────────┘${NC}"

        echo -e "\n${YELLOW}OVERVIEW${NC}"
        echo -e "This script provides comprehensive management of Mistral-Nemo-Instruct GGUF models:"
        echo -e " • ${GREEN}Download${NC} specific versions of Mistral-Nemo-Instruct GGUF from Hugging Face"
        echo -e " • ${GREEN}Upload${NC} models to cloud storage (AWS S3, Azure Blob, GCP Storage)"
        echo -e " • ${GREEN}Version management${NC} with automatic archiving of previous versions"
        echo -e " • ${GREEN}Multi-cloud support${NC} with provider-specific optimizations"
        echo -e " • ${GREEN}8-bit quantization${NC} (Q8_0) for optimal quality and performance"
        echo -e " • ${GREEN}GGUF format${NC} optimized for efficient inference"

        echo -e "\n${YELLOW}WORKFLOW EXAMPLES${NC}"
        echo -e "${GREEN}1. Basic Download:${NC}"
        echo -e "   Download the latest Mistral-Nemo-Instruct GGUF model from Hugging Face:"
        echo -e "   $ ./manage-mistral.sh --download --version=latest"
        echo -e "   This saves the model to: models/mistral-nemo/latest/"

        echo -e "\n${GREEN}2. Specific Version:${NC}"
        echo -e "   Download a specific version of Mistral-Nemo-Instruct GGUF:"
        echo -e "   $ ./manage-mistral.sh --download --version=2.0"
        echo -e "   This saves the model to: models/mistral-nemo/2.0/"

        echo -e "\n${GREEN}3. Download and Upload to AWS:${NC}"
        echo -e "   $ ./manage-mistral.sh --update --version=2.0 --aws \\"
        echo -e "     --bucket-name=my-models-bucket"
        echo -e "   This will:"
        echo -e "   - Download Mistral-Nemo-Instruct GGUF v2.0 from Hugging Face"
        echo -e "   - Upload it to S3 at: s3://my-models-bucket/models/mistral-nemo/2.0/"
        echo -e "   - Also update the 'latest' version in S3"

        echo -e "\n${YELLOW}CLOUD STORAGE FEATURES${NC}"
        echo -e "${GREEN}• Versioning:${NC}"
        echo -e "  The script maintains versioned copies of models in cloud storage."
        echo -e "  When updating an existing version, the previous version is"
        echo -e "  automatically archived with a timestamp."

        echo -e "\n${GREEN}• Latest Version:${NC}"
        echo -e "  When uploading a specific version, the script also updates the"
        echo -e "  'latest' version in cloud storage, making it easy to always use"
        echo -e "  the most recent model."

        echo -e "\n${GREEN}• Multi-Cloud Support:${NC}"
        echo -e "  The script supports three major cloud providers:"
        echo -e "  - AWS S3"
        echo -e "  - Azure Blob Storage"
        echo -e "  - Google Cloud Storage"

        echo -e "\n${YELLOW}DIRECTORY STRUCTURE${NC}"
        echo -e "${GREEN}Local Storage:${NC}"
        echo -e "  models/mistral-nemo/"
        echo -e "  ├── latest/           # Latest version of the model"
        echo -e "  │   ├── mistral-nemo-instruct-2407.Q8_0.gguf  # 8-bit quantized model"
        echo -e "  │   ├── *.json          # Model configuration files"
        echo -e "  │   └── version.txt     # Version information"
        echo -e "  └── 2.0/              # Specific version"
        echo -e "      ├── mistral-nemo-instruct-2407.Q8_0.gguf  # 8-bit quantized model"
        echo -e "      ├── *.json          # Model configuration files"
        echo -e "      └── version.txt     # Version information"

        echo -e "\n${GREEN}Cloud Storage:${NC}"
        echo -e "  models/mistral-nemo/"
        echo -e "  ├── latest/           # Latest version"
        echo -e "  ├── 2.0/              # Specific version"
        echo -e "  └── archives/         # Archived versions"
        echo -e "      └── 2.0-20230615123456/  # Timestamped archive"

        echo -e "\n${YELLOW}ADVANCED USAGE${NC}"
        echo -e "${GREEN}Custom Directories:${NC}"
        echo -e "  Specify custom local and cloud directories:"
        echo -e "  $ ./manage-mistral.sh --download --version=2.0 \\"
        echo -e "    --models-dir=/custom/path/to/models"

        echo -e "\n${GREEN}Cloud Provider Authentication:${NC}"
        echo -e "  The script uses the default authentication methods for each cloud provider:"
        echo -e "  - AWS: AWS CLI credentials (~/.aws/credentials)"
        echo -e "  - Azure: Azure CLI authentication"
        echo -e "  - GCP: gcloud authentication"

        echo -e "\n${YELLOW}FOR MORE HELP${NC}"
        echo -e "For specific help on commands, try:"
        echo -e "  $ ./manage-mistral.sh --download help"
        echo -e "  $ ./manage-mistral.sh --update help"
        echo -e "  $ ./manage-mistral.sh --update --aws help"
        return
    fi

    # General help if no specific context
    if [ -z "$context" ]; then
        echo -e "\nUsage:"
        echo "  ./manage-mistral.sh --download --version=latest"
        echo "  ./manage-mistral.sh --download --version=x.x"
        echo "  ./manage-mistral.sh --update --version=x.x --cloud-provider=aws"
        echo -e "\nOptions:"
        echo "  --download             Download the model from Hugging Face"
        echo "  --update               Download and upload the model to cloud storage"
        echo "  --help                 Show this help message"
        echo "  readme                 Show detailed documentation"
        echo -e "\nFor more specific help, try:"
        echo "  ./manage-mistral.sh --download help"
        echo "  ./manage-mistral.sh --update help"
        echo "  ./manage-mistral.sh --update --aws help"
        echo "  ./manage-mistral.sh readme"
        return
    fi

    # Download-specific help
    if [ "$context" = "download" ]; then
        echo -e "\nDownload Usage:"
        echo "  ./manage-mistral.sh --download --version=latest"
        echo "  ./manage-mistral.sh --download --version=x.x"
        echo -e "\nDownload Options:"
        echo "  --version=VERSION      Model version (latest or specific version like 2.0)"
        echo "  --models-dir=DIR       Local directory for models (default: models/mistral-nemo)"
        echo "  --help                 Show this help message"
        echo -e "\nExamples:"
        echo "  ./manage-mistral.sh --download --version=latest"
        echo "  ./manage-mistral.sh --download --version=2.0 --models-dir=/custom/path"
        return
    fi

    # Update-specific help
    if [ "$context" = "update" ]; then
        echo -e "\nUpdate Usage:"
        echo "  ./manage-mistral.sh --update --version=x.x --cloud-provider=PROVIDER"
        echo -e "\nUpdate Options:"
        echo "  --version=VERSION      Model version (latest or specific version like 2.0)"
        echo "  --cloud-provider=PROVIDER  Cloud provider for upload (aws, azure, gcp)"
        echo "  --models-dir=DIR       Local directory for models (default: models/mistral-nemo)"
        echo "  --models-path=PATH     Path in cloud storage (default: models/mistral-nemo)"
        echo "  --help                 Show this help message"

        # If a specific provider is requested
        if [ ! -z "$provider" ]; then
            case $provider in
                aws)
                    echo -e "\nAWS-specific options:"
                    echo "  --aws-region=REGION    AWS Region (default: ap-southeast-2)"
                    echo "  --aws-account-id=ID    AWS Account ID"
                    echo "  --bucket-name=NAME     S3 Bucket name"
                    echo -e "\nExample:"
                    echo "  ./manage-mistral.sh --update --version=2.0 --cloud-provider=aws --bucket-name=my-models-bucket"
                    ;;
                azure)
                    echo -e "\nAzure-specific options:"
                    echo "  --azure-storage-account=NAME  Azure Storage Account"
                    echo "  --azure-container-name=NAME   Azure Container Name (default: models)"
                    echo -e "\nExample:"
                    echo "  ./manage-mistral.sh --update --version=2.0 --cloud-provider=azure --azure-storage-account=mystorageaccount"
                    ;;
                gcp)
                    echo -e "\nGCP-specific options:"
                    echo "  --gcp-project-id=ID    GCP Project ID"
                    echo "  --gcp-bucket-name=NAME GCP Bucket Name"
                    echo -e "\nExample:"
                    echo "  ./manage-mistral.sh --update --version=2.0 --cloud-provider=gcp --gcp-project-id=my-project --gcp-bucket-name=my-bucket"
                    ;;
                *)
                    echo -e "\nSupported cloud providers: aws, azure, gcp"
                    ;;
            esac
        else
            echo -e "\nFor cloud-specific help, try:"
            echo "  ./manage-mistral.sh --update --aws help"
            echo "  ./manage-mistral.sh --update --azure help"
            echo "  ./manage-mistral.sh --update --gcp help"
        fi
        return
    fi

    # If we get here, show general help
    echo -e "\nUnknown help context: $context"
    echo "Try './manage-mistral.sh --help' for general help"
}

# Function to exit with usage information
usage() {
    show_help
    exit 1
}

# Variables to track help requests
HELP_REQUESTED=false
HELP_CONTEXT=""
HELP_PROVIDER=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --download)
      ACTION="download"
      shift
      ;;
    --update)
      ACTION="update"
      shift
      ;;
    --aws)
      CLOUD_PROVIDER="aws"
      shift
      ;;
    --azure)
      CLOUD_PROVIDER="azure"
      shift
      ;;
    --gcp)
      CLOUD_PROVIDER="gcp"
      shift
      ;;
    --version=*)
      VERSION="${1#*=}"
      shift
      ;;
    --cloud-provider=*)
      CLOUD_PROVIDER="${1#*=}"
      shift
      ;;
    --models-dir=*)
      MODELS_DIR="${1#*=}"
      shift
      ;;
    --models-path=*)
      MODELS_PATH="${1#*=}"
      shift
      ;;
    # AWS-specific options
    --aws-region=*)
      AWS_REGION="${1#*=}"
      shift
      ;;
    --aws-account-id=*)
      AWS_ACCOUNT_ID="${1#*=}"
      shift
      ;;
    --bucket-name=*)
      BUCKET_NAME="${1#*=}"
      shift
      ;;
    # Azure-specific options
    --azure-storage-account=*)
      AZURE_STORAGE_ACCOUNT="${1#*=}"
      shift
      ;;
    --azure-container-name=*)
      AZURE_CONTAINER_NAME="${1#*=}"
      shift
      ;;
    # GCP-specific options
    --gcp-project-id=*)
      GCP_PROJECT_ID="${1#*=}"
      shift
      ;;
    --gcp-bucket-name=*)
      GCP_BUCKET_NAME="${1#*=}"
      shift
      ;;
    help|--help|-h)
      HELP_REQUESTED=true
      shift
      ;;
    readme)
      HELP_REQUESTED=true
      HELP_CONTEXT="readme"
      shift
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      usage
      ;;
  esac
done

# Handle help requests
if [ "$HELP_REQUESTED" = true ]; then
  if [ ! -z "$ACTION" ]; then
    HELP_CONTEXT="$ACTION"
    if [ ! -z "$CLOUD_PROVIDER" ]; then
      HELP_PROVIDER="$CLOUD_PROVIDER"
    fi
  fi
  show_help "$HELP_CONTEXT" "$HELP_PROVIDER"
  exit 0
fi

# Validate required parameters
if [ -z "$ACTION" ]; then
  echo -e "${RED}Error: Action (--download or --update) is required${NC}"
  usage
fi

if [ -z "$VERSION" ]; then
  echo -e "${RED}Error: Version (--version=latest or --version=x.x) is required${NC}"
  usage
fi

# Check if Python and pip are installed
if ! command -v python3 &> /dev/null; then
  echo -e "${RED}Error: Python 3 is not installed. Please install Python 3 first.${NC}"
  exit 1
fi

if ! command -v pip3 &> /dev/null; then
  echo -e "${RED}Error: pip3 is not installed. Please install pip3 first.${NC}"
  exit 1
fi

# Ensure transformers is installed
if ! python3 -c "import transformers" &> /dev/null; then
  echo -e "${YELLOW}Installing transformers library...${NC}"
  pip3 install transformers
fi

# Create models directory if it doesn't exist
mkdir -p "$MODELS_DIR"

# Function to download the model
download_model() {
  local version=$1
  local target_dir=$2

  echo -e "${GREEN}Downloading Mistral-Nemo-Instruct GGUF model...${NC}"

  # Create the target directory if it doesn't exist
  mkdir -p "$target_dir"

  # Check if Python and pip are installed
  if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed. Please install Python 3 first.${NC}"
    exit 1
  fi

  if ! command -v pip3 &> /dev/null; then
    echo -e "${RED}Error: pip3 is not installed. Please install pip3 first.${NC}"
    exit 1
  fi

  # Ensure huggingface_hub is installed
  if ! python3 -c "import huggingface_hub" &> /dev/null; then
    echo -e "${YELLOW}Installing huggingface_hub library...${NC}"
    pip3 install huggingface_hub
  fi

  # Create a Python script to download the model files
  cat > /tmp/download_mistral_gguf.py << EOF
from huggingface_hub import hf_hub_download, list_repo_files
import os
import sys

model_id = "$MODEL_NAME"
target_dir = "$target_dir"

try:
    # First, list all files in the repository
    print(f"Listing files in {model_id} repository...")
    all_files = list_repo_files(model_id)
    print(f"Found {len(all_files)} files in repository")

    # Look for Q8_0 GGUF file
    q8_files = [f for f in all_files if 'Q8_0' in f and f.endswith('.gguf')]
    if not q8_files:
        print("No Q8_0 GGUF files found in repository!")
        # Fall back to any GGUF file if Q8_0 not found
        gguf_files = [f for f in all_files if f.endswith('.gguf')]
        if not gguf_files:
            print("No GGUF files found in repository!")
            sys.exit(1)
        print(f"Falling back to available GGUF file: {gguf_files[0]}")
        model_file = gguf_files[0]
    else:
        print(f"Found Q8_0 GGUF file: {q8_files[0]}")
        model_file = q8_files[0]

    # Download the model file
    print(f"Downloading {model_file} to {target_dir}...")
    hf_hub_download(
        repo_id=model_id,
        filename=model_file,
        local_dir=target_dir,
        local_dir_use_symlinks=False
    )

    # Also download essential JSON files (tokenizer, config)
    json_files = [f for f in all_files if f.endswith('.json')]
    for json_file in json_files:
        print(f"Downloading {json_file} to {target_dir}...")
        hf_hub_download(
            repo_id=model_id,
            filename=json_file,
            local_dir=target_dir,
            local_dir_use_symlinks=False
        )

    print("Download completed successfully!")
except Exception as e:
    print(f"Error downloading model: {e}", file=sys.stderr)
    sys.exit(1)
EOF

  # Run the Python script
  python3 /tmp/download_mistral_gguf.py

  # Clean up
  rm /tmp/download_mistral_gguf.py

  # Create a version file
  echo "$version" > "$target_dir/version.txt"

  echo -e "${GREEN}Mistral-Nemo-Instruct GGUF model downloaded successfully to $target_dir${NC}"
}

# Function to upload the model to cloud storage
upload_model() {
  local version=$1
  local source_dir=$2

  # Create timestamp for versioning
  TIMESTAMP=$(date +%Y%m%d%H%M%S)

  echo -e "${GREEN}Uploading DistilBERT model to cloud storage...${NC}"

  # Upload models based on cloud provider
  case "$CLOUD_PROVIDER" in
    aws)
      # Validate AWS parameters
      if [ -z "$AWS_ACCOUNT_ID" ] && [ -z "$BUCKET_NAME" ]; then
        echo -e "${RED}Error: Either AWS_ACCOUNT_ID or BUCKET_NAME must be provided for AWS${NC}"
        exit 1
      fi

      echo "Using AWS S3 bucket: $BUCKET_NAME"
      echo "Using models directory: $source_dir"

      # Check if the bucket exists
      if ! aws s3 ls "s3://$BUCKET_NAME" >/dev/null 2>&1; then
        echo "Creating bucket: $BUCKET_NAME"
        aws s3 mb "s3://$BUCKET_NAME" --region "$AWS_REGION"

        # Enable versioning
        aws s3api put-bucket-versioning \
          --bucket "$BUCKET_NAME" \
          --versioning-configuration Status=Enabled

        # Block public access
        aws s3api put-public-access-block \
          --bucket "$BUCKET_NAME" \
          --public-access-block-configuration "BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true"
      fi

      # Check if version directory exists
      if aws s3 ls "s3://$BUCKET_NAME/$MODELS_PATH/$version/" >/dev/null 2>&1; then
        # Archive current version to timestamped directory
        echo "Archiving current version to $MODELS_PATH/archives/$version-$TIMESTAMP/"
        aws s3 sync "s3://$BUCKET_NAME/$MODELS_PATH/$version/" "s3://$BUCKET_NAME/$MODELS_PATH/archives/$version-$TIMESTAMP/"
      fi

      # Upload new models to version directory
      echo "Uploading new models to $MODELS_PATH/$version/"
      aws s3 sync "$source_dir/" "s3://$BUCKET_NAME/$MODELS_PATH/$version/"

      # If version is not "latest", also update the latest directory
      if [ "$version" != "latest" ]; then
        echo "Updating latest version to $version"
        aws s3 sync "$source_dir/" "s3://$BUCKET_NAME/$MODELS_PATH/latest/"
      fi
      ;;

    azure)
      # Validate Azure parameters
      if [ -z "$AZURE_STORAGE_ACCOUNT" ]; then
        echo -e "${RED}Error: AZURE_STORAGE_ACCOUNT must be provided for Azure${NC}"
        exit 1
      fi

      echo "Using Azure Storage Account: $AZURE_STORAGE_ACCOUNT"
      echo "Using Azure Container: $AZURE_CONTAINER_NAME"
      echo "Using models directory: $source_dir"

      # Check if the container exists
      if ! az storage container exists --account-name "$AZURE_STORAGE_ACCOUNT" --name "$AZURE_CONTAINER_NAME" --query exists --output tsv | grep -q "True"; then
        echo "Creating container: $AZURE_CONTAINER_NAME"
        az storage container create --account-name "$AZURE_STORAGE_ACCOUNT" --name "$AZURE_CONTAINER_NAME"
      fi

      # Check if version directory exists
      if az storage blob list --account-name "$AZURE_STORAGE_ACCOUNT" --container-name "$AZURE_CONTAINER_NAME" --prefix "$MODELS_PATH/$version/" --query "[].name" --output tsv | grep -q .; then
        # Archive current version to timestamped directory
        echo "Archiving current version to $MODELS_PATH/archives/$version-$TIMESTAMP/"
        az storage blob copy start-batch \
          --account-name "$AZURE_STORAGE_ACCOUNT" \
          --source-container "$AZURE_CONTAINER_NAME" \
          --destination-container "$AZURE_CONTAINER_NAME" \
          --source-prefix "$MODELS_PATH/$version/" \
          --destination-prefix "$MODELS_PATH/archives/$version-$TIMESTAMP/"
      fi

      # Upload new models to version directory
      echo "Uploading new models to $MODELS_PATH/$version/"
      az storage blob upload-batch \
        --account-name "$AZURE_STORAGE_ACCOUNT" \
        --destination "$AZURE_CONTAINER_NAME" \
        --destination-path "$MODELS_PATH/$version" \
        --source "$source_dir"

      # If version is not "latest", also update the latest directory
      if [ "$version" != "latest" ]; then
        echo "Updating latest version to $version"
        az storage blob upload-batch \
          --account-name "$AZURE_STORAGE_ACCOUNT" \
          --destination "$AZURE_CONTAINER_NAME" \
          --destination-path "$MODELS_PATH/latest" \
          --source "$source_dir"
      fi
      ;;

    gcp)
      # Validate GCP parameters
      if [ -z "$GCP_BUCKET_NAME" ]; then
        echo -e "${RED}Error: GCP_BUCKET_NAME must be provided for GCP${NC}"
        exit 1
      fi

      echo "Using GCP bucket: $GCP_BUCKET_NAME"
      echo "Using models directory: $source_dir"

      # Check if the bucket exists
      if ! gsutil ls -b "gs://$GCP_BUCKET_NAME" >/dev/null 2>&1; then
        echo "Creating bucket: $GCP_BUCKET_NAME"
        gsutil mb -l us-central1 "gs://$GCP_BUCKET_NAME"

        # Enable versioning
        gsutil versioning set on "gs://$GCP_BUCKET_NAME"
      fi

      # Check if version directory exists
      if gsutil ls "gs://$GCP_BUCKET_NAME/$MODELS_PATH/$version/" >/dev/null 2>&1; then
        # Archive current version to timestamped directory
        echo "Archiving current version to $MODELS_PATH/archives/$version-$TIMESTAMP/"
        gsutil -m cp -r "gs://$GCP_BUCKET_NAME/$MODELS_PATH/$version/*" "gs://$GCP_BUCKET_NAME/$MODELS_PATH/archives/$version-$TIMESTAMP/"
      fi

      # Upload new models to version directory
      echo "Uploading new models to $MODELS_PATH/$version/"
      gsutil -m cp -r "$source_dir/*" "gs://$GCP_BUCKET_NAME/$MODELS_PATH/$version/"

      # If version is not "latest", also update the latest directory
      if [ "$version" != "latest" ]; then
        echo "Updating latest version to $version"
        gsutil -m cp -r "$source_dir/*" "gs://$GCP_BUCKET_NAME/$MODELS_PATH/latest/"
      fi
      ;;

    *)
      echo -e "${RED}Error: Unsupported cloud provider: $CLOUD_PROVIDER${NC}"
      echo "Supported providers: aws, azure, gcp"
      exit 1
      ;;
  esac

  echo -e "${GREEN}Mistral-Nemo-Instruct GGUF model uploaded successfully${NC}"
}

# Main execution
if [ "$VERSION" = "latest" ]; then
  TARGET_DIR="$MODELS_DIR/latest"
else
  TARGET_DIR="$MODELS_DIR/$VERSION"
fi

# Create target directory if it doesn't exist
mkdir -p "$TARGET_DIR"

# Execute the requested action
case "$ACTION" in
  download)
    download_model "$VERSION" "$TARGET_DIR"
    ;;
  update)
    download_model "$VERSION" "$TARGET_DIR"
    upload_model "$VERSION" "$TARGET_DIR"
    ;;
  *)
    echo -e "${RED}Error: Invalid action: $ACTION${NC}"
    usage
    ;;
esac

echo -e "${GREEN}Mistral-Nemo-Instruct GGUF model management completed successfully${NC}"
