#!/bin/bash

# This script manages RouteLL-BERT models
# It can download, upload, and rollback models to/from cloud storage
# Usage:
#   --setup                : Setup the model locally
#   --download             : Download the model from cloud storage
#   --upload               : Upload the model to cloud storage
#   --rollback             : Rollback to a previous version

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default values
ACTION=""
VERSION=${VERSION:-"latest"}
CLOUD_PROVIDER=${CLOUD_PROVIDER:-"aws"}
MODELS_DIR="../models/routellm-bert"
MODELS_PATH="models/routellm-bert"
FORCE_DOWNLOAD=${FORCE_DOWNLOAD:-"false"}

# AWS-specific defaults
AWS_REGION=${AWS_REGION:-"ap-southeast-2"}
AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID:-""}
BUCKET_NAME=${MODELS_BUCKET_NAME:-"difinity-flow-models-$AWS_ACCOUNT_ID"}

# Azure-specific defaults
AZURE_STORAGE_ACCOUNT=${AZURE_STORAGE_ACCOUNT:-""}
AZURE_CONTAINER_NAME=${AZURE_CONTAINER_NAME:-"models"}

# GCP-specific defaults
GCP_PROJECT_ID=${GCP_PROJECT_ID:-""}
GCP_BUCKET_NAME=${GCP_BUCKET_NAME:-""}
GCP_REGION=${GCP_REGION:-"us-central1"}

# Environment defaults
ENVIRONMENT=${ENVIRONMENT:-"production"}
APP_NAME=${APP_NAME:-"difinity-flow"}

# Variables to track help requests
HELP_REQUESTED=false
HELP_CONTEXT=""
HELP_PROVIDER=""

# Function to display context-specific help information
show_help() {
    local context=$1
    local provider=$2

    echo -e "${GREEN}RouteLL-BERT Model Management Script${NC}"

    # README section if explicitly requested
    if [ "$context" = "readme" ]; then
        echo -e "\n${GREEN}┌─────────────────────────────────────────────────────────────┐${NC}"
        echo -e "${GREEN}│                     DETAILED README                          │${NC}"
        echo -e "${GREEN}└─────────────────────────────────────────────────────────────┘${NC}"

        echo -e "\n${YELLOW}OVERVIEW${NC}"
        echo -e "This script provides comprehensive management of RouteLL-BERT models:"
        echo -e " • ${GREEN}Setup${NC} the model locally"
        echo -e " • ${GREEN}Download${NC} models from cloud storage"
        echo -e " • ${GREEN}Upload${NC} models to cloud storage (AWS S3, Azure Blob, GCP Storage)"
        echo -e " • ${GREEN}Rollback${NC} to previous versions"
        echo -e " • ${GREEN}Multi-cloud support${NC} with provider-specific optimizations"

        echo -e "\n${YELLOW}WORKFLOW EXAMPLES${NC}"
        echo -e "${GREEN}1. Setup Model:${NC}"
        echo -e "   Setup the RouteLL-BERT model locally:"
        echo -e "   $ ./manage-routellm-bert.sh --setup"
        echo -e "   This sets up the model in: models/routellm-bert/"

        echo -e "\n${GREEN}2. Download Model:${NC}"
        echo -e "   Download the model from cloud storage:"
        echo -e "   $ ./manage-routellm-bert.sh --download --version=latest"
        echo -e "   This downloads the model to: models/routellm-bert/latest/"

        echo -e "\n${GREEN}3. Upload Model:${NC}"
        echo -e "   Upload the model to cloud storage:"
        echo -e "   $ ./manage-routellm-bert.sh --upload --version=2.0 --aws \\"
        echo -e "     --bucket-name=my-models-bucket"
        echo -e "   This will upload the model to: s3://my-models-bucket/models/routellm-bert/2.0/"

        echo -e "\n${GREEN}4. Upload Model Directly to S3:${NC}"
        echo -e "   Upload the model directly to S3 (AWS only):"
        echo -e "   $ ./manage-routellm-bert.sh --upload-to-s3 --aws-account-id=************"
        echo -e "   This will upload the model to S3 with versioning and backup"

        echo -e "\n${GREEN}5. Rollback Model:${NC}"
        echo -e "   Rollback to a previous version:"
        echo -e "   $ ./manage-routellm-bert.sh --rollback --aws"
        echo -e "   This will rollback to the previous version of the model"

        echo -e "\n${YELLOW}CLOUD STORAGE FEATURES${NC}"
        echo -e "${GREEN}• Versioning:${NC}"
        echo -e "  The script maintains versioned copies of models in cloud storage."
        echo -e "  When updating an existing version, the previous version is"
        echo -e "  automatically archived with a timestamp."

        echo -e "\n${GREEN}• Latest Version:${NC}"
        echo -e "  When uploading a specific version, the script also updates the"
        echo -e "  'latest' version in cloud storage, making it easy to always use"
        echo -e "  the most recent model."

        echo -e "\n${GREEN}• Multi-Cloud Support:${NC}"
        echo -e "  The script supports three major cloud providers:"
        echo -e "  - AWS S3"
        echo -e "  - Azure Blob Storage"
        echo -e "  - Google Cloud Storage"

        echo -e "\n${YELLOW}DIRECTORY STRUCTURE${NC}"
        echo -e "${GREEN}Local Storage:${NC}"
        echo -e "  models/routellm-bert/"
        echo -e "  ├── latest/           # Latest version of the model"
        echo -e "  │   ├── config.json"
        echo -e "  │   ├── pytorch_model.bin"
        echo -e "  │   ├── tokenizer.json"
        echo -e "  │   └── version.txt"
        echo -e "  └── 2.0/              # Specific version"
        echo -e "      ├── config.json"
        echo -e "      ├── pytorch_model.bin"
        echo -e "      ├── tokenizer.json"
        echo -e "      └── version.txt"

        echo -e "\n${GREEN}Cloud Storage:${NC}"
        echo -e "  models/routellm-bert/"
        echo -e "  ├── latest/           # Latest version"
        echo -e "  ├── 2.0/              # Specific version"
        echo -e "  └── archives/         # Archived versions"
        echo -e "      └── 2.0-20230615123456/  # Timestamped archive"

        echo -e "\n${YELLOW}ADVANCED USAGE${NC}"
        echo -e "${GREEN}Custom Directories:${NC}"
        echo -e "  Specify custom local and cloud directories:"
        echo -e "  $ ./manage-routellm-bert.sh --download --version=2.0 \\"
        echo -e "    --models-dir=/custom/path/to/models"

        echo -e "\n${GREEN}Force Download:${NC}"
        echo -e "  Force download even if the model already exists locally:"
        echo -e "  $ ./manage-routellm-bert.sh --download --force"

        echo -e "\n${GREEN}Cloud Provider Authentication:${NC}"
        echo -e "  The script uses the default authentication methods for each cloud provider:"
        echo -e "  - AWS: AWS CLI credentials (~/.aws/credentials)"
        echo -e "  - Azure: Azure CLI authentication"
        echo -e "  - GCP: gcloud authentication"

        echo -e "\n${YELLOW}FOR MORE HELP${NC}"
        echo -e "For specific help on commands, try:"
        echo -e "  $ ./manage-routellm-bert.sh --setup help"
        echo -e "  $ ./manage-routellm-bert.sh --download help"
        echo -e "  $ ./manage-routellm-bert.sh --upload help"
        echo -e "  $ ./manage-routellm-bert.sh --rollback help"
        echo -e "  $ ./manage-routellm-bert.sh --upload --aws help"
        return
    fi

    # General help if no specific context
    if [ -z "$context" ]; then
        echo -e "\nUsage:"
        echo "  ./manage-routellm-bert.sh --setup"
        echo "  ./manage-routellm-bert.sh --download [--version=latest] [--force]"
        echo "  ./manage-routellm-bert.sh --upload --version=x.x [--cloud-provider=aws]"
        echo "  ./manage-routellm-bert.sh --upload-to-s3 [--aws-account-id=ID] [--aws-region=REGION]"
        echo "  ./manage-routellm-bert.sh --rollback [--cloud-provider=aws]"
        echo -e "\nOptions:"
        echo "  --setup                Setup the model locally"
        echo "  --download             Download the model from cloud storage"
        echo "  --upload               Upload the model to cloud storage"
        echo "  --upload-to-s3         Upload the model directly to S3 (AWS only)"
        echo "  --rollback             Rollback to a previous version"
        echo "  --version=VERSION      Model version (default: latest)"
        echo "  --force                Force download even if model exists"
        echo "  --cloud-provider=PROVIDER  Cloud provider (aws, azure, gcp)"
        echo "  --help                 Show this help message"
        echo "  readme                 Show detailed documentation"
        echo -e "\nFor more specific help, try:"
        echo "  ./manage-routellm-bert.sh --setup help"
        echo "  ./manage-routellm-bert.sh --download help"
        echo "  ./manage-routellm-bert.sh --upload help"
        echo "  ./manage-routellm-bert.sh --upload-to-s3 help"
        echo "  ./manage-routellm-bert.sh --rollback help"
        echo "  ./manage-routellm-bert.sh readme"
        return
    fi

    # Setup-specific help
    if [ "$context" = "setup" ]; then
        echo -e "\nSetup Usage:"
        echo "  ./manage-routellm-bert.sh --setup"
        echo -e "\nSetup Options:"
        echo "  --models-dir=DIR       Local directory for models (default: models/routellm-bert)"
        echo "  --help                 Show this help message"
        echo -e "\nExamples:"
        echo "  ./manage-routellm-bert.sh --setup"
        echo "  ./manage-routellm-bert.sh --setup --models-dir=/custom/path/to/models"
        return
    fi

    # Download-specific help
    if [ "$context" = "download" ]; then
        echo -e "\nDownload Usage:"
        echo "  ./manage-routellm-bert.sh --download [--version=latest] [--force]"
        echo -e "\nDownload Options:"
        echo "  --version=VERSION      Model version (default: latest)"
        echo "  --force                Force download even if model exists"
        echo "  --cloud-provider=PROVIDER  Cloud provider (aws, azure, gcp)"
        echo "  --models-dir=DIR       Local directory for models (default: models/routellm-bert)"
        echo "  --models-path=PATH     Path in cloud storage (default: models/routellm-bert)"
        echo "  --help                 Show this help message"

        # If a specific provider is requested
        if [ ! -z "$provider" ]; then
            case $provider in
                aws)
                    echo -e "\nAWS-specific options:"
                    echo "  --aws-region=REGION    AWS Region (default: ap-southeast-2)"
                    echo "  --aws-account-id=ID    AWS Account ID"
                    echo "  --bucket-name=NAME     S3 Bucket name"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --download --version=latest --aws --bucket-name=my-models-bucket"
                    ;;
                azure)
                    echo -e "\nAzure-specific options:"
                    echo "  --azure-storage-account=NAME  Azure Storage Account"
                    echo "  --azure-container-name=NAME   Azure Container Name (default: models)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --download --version=latest --azure --azure-storage-account=mystorageaccount"
                    ;;
                gcp)
                    echo -e "\nGCP-specific options:"
                    echo "  --gcp-project-id=ID    GCP Project ID"
                    echo "  --gcp-bucket-name=NAME GCP Bucket Name"
                    echo "  --gcp-region=REGION    GCP Region (default: us-central1)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --download --version=latest --gcp --gcp-project-id=my-project --gcp-bucket-name=my-bucket"
                    ;;
            esac
        else
            echo -e "\nFor cloud-specific help, try:"
            echo "  ./manage-routellm-bert.sh --download --aws help"
            echo "  ./manage-routellm-bert.sh --download --azure help"
            echo "  ./manage-routellm-bert.sh --download --gcp help"
        fi
        return
    fi

    # Upload-specific help
    if [ "$context" = "upload" ]; then
        echo -e "\nUpload Usage:"
        echo "  ./manage-routellm-bert.sh --upload --version=x.x [--cloud-provider=PROVIDER]"
        echo -e "\nUpload Options:"
        echo "  --version=VERSION      Model version (required)"
        echo "  --cloud-provider=PROVIDER  Cloud provider (aws, azure, gcp)"
        echo "  --models-dir=DIR       Local directory for models (default: models/routellm-bert)"
        echo "  --models-path=PATH     Path in cloud storage (default: models/routellm-bert)"
        echo "  --help                 Show this help message"

        # If a specific provider is requested
        if [ ! -z "$provider" ]; then
            case $provider in
                aws)
                    echo -e "\nAWS-specific options:"
                    echo "  --aws-region=REGION    AWS Region (default: ap-southeast-2)"
                    echo "  --aws-account-id=ID    AWS Account ID"
                    echo "  --bucket-name=NAME     S3 Bucket name"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --upload --version=2.0 --aws --bucket-name=my-models-bucket"
                    ;;
                azure)
                    echo -e "\nAzure-specific options:"
                    echo "  --azure-storage-account=NAME  Azure Storage Account"
                    echo "  --azure-container-name=NAME   Azure Container Name (default: models)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --upload --version=2.0 --azure --azure-storage-account=mystorageaccount"
                    ;;
                gcp)
                    echo -e "\nGCP-specific options:"
                    echo "  --gcp-project-id=ID    GCP Project ID"
                    echo "  --gcp-bucket-name=NAME GCP Bucket Name"
                    echo "  --gcp-region=REGION    GCP Region (default: us-central1)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --upload --version=2.0 --gcp --gcp-project-id=my-project --gcp-bucket-name=my-bucket"
                    ;;
            esac
        else
            echo -e "\nFor cloud-specific help, try:"
            echo "  ./manage-routellm-bert.sh --upload --aws help"
            echo "  ./manage-routellm-bert.sh --upload --azure help"
            echo "  ./manage-routellm-bert.sh --upload --gcp help"
        fi
        return
    fi

    # Upload-to-S3-specific help
    if [ "$context" = "upload-to-s3" ]; then
        echo -e "\nUpload to S3 Usage:"
        echo "  ./manage-routellm-bert.sh --upload-to-s3 [--aws-account-id=ID] [--aws-region=REGION]"
        echo -e "\nUpload to S3 Options:"
        echo "  --aws-account-id=ID    AWS Account ID"
        echo "  --aws-region=REGION    AWS Region (default: from AWS CLI config)"
        echo "  --help                 Show this help message"
        echo -e "\nDescription:"
        echo "  This command uploads the RouteLL-BERT model directly to S3 using the"
        echo "  upload_models_to_s3.sh script. It creates a versioned backup of any"
        echo "  existing models and uploads the new models to the 'latest' directory."
        echo -e "\nExample:"
        echo "  ./manage-routellm-bert.sh --upload-to-s3 --aws-account-id=************ --aws-region=us-west-2"
        return
    fi

    # Rollback-specific help
    if [ "$context" = "rollback" ]; then
        echo -e "\nRollback Usage:"
        echo "  ./manage-routellm-bert.sh --rollback [--cloud-provider=PROVIDER]"
        echo -e "\nRollback Options:"
        echo "  --cloud-provider=PROVIDER  Cloud provider (aws, azure, gcp)"
        echo "  --app-name=NAME        Application name (default: difinity-flow)"
        echo "  --environment=ENV      Environment (default: production)"
        echo "  --help                 Show this help message"

        # If a specific provider is requested
        if [ ! -z "$provider" ]; then
            case $provider in
                aws)
                    echo -e "\nAWS-specific options:"
                    echo "  --aws-region=REGION    AWS Region (default: ap-southeast-2)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --rollback --aws"
                    ;;
                azure)
                    echo -e "\nAzure-specific options:"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --rollback --azure"
                    ;;
                gcp)
                    echo -e "\nGCP-specific options:"
                    echo "  --gcp-region=REGION    GCP Region (default: us-central1)"
                    echo -e "\nExample:"
                    echo "  ./manage-routellm-bert.sh --rollback --gcp"
                    ;;
            esac
        else
            echo -e "\nFor cloud-specific help, try:"
            echo "  ./manage-routellm-bert.sh --rollback --aws help"
            echo "  ./manage-routellm-bert.sh --rollback --azure help"
            echo "  ./manage-routellm-bert.sh --rollback --gcp help"
        fi
        return
    fi

    # If we get here, show general help
    echo -e "\nUnknown help context: $context"
    echo "Try './manage-routellm-bert.sh --help' for general help"
}

# Function to exit with usage information
usage() {
    show_help
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --setup)
      ACTION="setup"
      shift
      ;;
    --download)
      ACTION="download"
      shift
      ;;
    --upload)
      ACTION="upload"
      shift
      ;;
    --upload-to-s3)
      ACTION="upload-to-s3"
      shift
      ;;
    --rollback)
      ACTION="rollback"
      shift
      ;;
    --aws)
      CLOUD_PROVIDER="aws"
      shift
      ;;
    --azure)
      CLOUD_PROVIDER="azure"
      shift
      ;;
    --gcp)
      CLOUD_PROVIDER="gcp"
      shift
      ;;
    --version=*)
      VERSION="${1#*=}"
      shift
      ;;
    --force)
      FORCE_DOWNLOAD="true"
      shift
      ;;
    --cloud-provider=*)
      CLOUD_PROVIDER="${1#*=}"
      shift
      ;;
    --models-dir=*)
      MODELS_DIR="${1#*=}"
      shift
      ;;
    --models-path=*)
      MODELS_PATH="${1#*=}"
      shift
      ;;
    # AWS-specific options
    --aws-region=*)
      AWS_REGION="${1#*=}"
      shift
      ;;
    --aws-account-id=*)
      AWS_ACCOUNT_ID="${1#*=}"
      shift
      ;;
    --bucket-name=*)
      BUCKET_NAME="${1#*=}"
      shift
      ;;
    # Azure-specific options
    --azure-storage-account=*)
      AZURE_STORAGE_ACCOUNT="${1#*=}"
      shift
      ;;
    --azure-container-name=*)
      AZURE_CONTAINER_NAME="${1#*=}"
      shift
      ;;
    # GCP-specific options
    --gcp-project-id=*)
      GCP_PROJECT_ID="${1#*=}"
      shift
      ;;
    --gcp-bucket-name=*)
      GCP_BUCKET_NAME="${1#*=}"
      shift
      ;;
    --gcp-region=*)
      GCP_REGION="${1#*=}"
      shift
      ;;
    # Application options
    --app-name=*)
      APP_NAME="${1#*=}"
      shift
      ;;
    --environment=*)
      ENVIRONMENT="${1#*=}"
      shift
      ;;
    help|--help|-h)
      HELP_REQUESTED=true
      shift
      ;;
    readme)
      HELP_REQUESTED=true
      HELP_CONTEXT="readme"
      shift
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      usage
      ;;
  esac
done

# Handle help requests
if [ "$HELP_REQUESTED" = true ]; then
  if [ ! -z "$ACTION" ]; then
    HELP_CONTEXT="$ACTION"
    if [ ! -z "$CLOUD_PROVIDER" ]; then
      HELP_PROVIDER="$CLOUD_PROVIDER"
    fi
  fi
  show_help "$HELP_CONTEXT" "$HELP_PROVIDER"
  exit 0
fi

# Validate required parameters
if [ -z "$ACTION" ]; then
  echo -e "${RED}Error: Action (--setup, --download, --upload, or --rollback) is required${NC}"
  usage
fi

# Create target directory if it doesn't exist
mkdir -p "$MODELS_DIR"

# Function to setup the model
setup_model() {
  echo -e "${GREEN}Setting up RouteLL-BERT model...${NC}"

  # Create the model directory
  mkdir -p "$MODELS_DIR"

  # Run the setup_bert.sh script
  echo "Running setup_bert.sh script..."

  # Set environment variables for the setup script
  export MODELS_DIR="$MODELS_DIR"

  # Run the setup script
  ./routellm-bert/setup_bert.sh

  echo -e "${GREEN}RouteLL-BERT model setup completed successfully${NC}"
}

# Function to download the model from cloud storage
download_model() {
  local version=$1
  local force=$2
  local target_dir="$MODELS_DIR/$version"

  echo -e "${GREEN}Downloading RouteLL-BERT model version $version from cloud storage...${NC}"

  # Create the target directory
  mkdir -p "$target_dir"

  # Set environment variables for the download script
  export MODELS_DIR="$MODELS_DIR"
  export MODELS_PATH="$MODELS_PATH"
  export VERSION="$version"
  export FORCE_DOWNLOAD="$force"
  export CLOUD_PROVIDER="$CLOUD_PROVIDER"
  export AWS_REGION="$AWS_REGION"
  export BUCKET_NAME="$BUCKET_NAME"
  export AZURE_STORAGE_ACCOUNT="$AZURE_STORAGE_ACCOUNT"
  export AZURE_CONTAINER_NAME="$AZURE_CONTAINER_NAME"
  export GCP_PROJECT_ID="$GCP_PROJECT_ID"
  export GCP_BUCKET_NAME="$GCP_BUCKET_NAME"
  export GCP_REGION="$GCP_REGION"

  # Run the download script
  ./routellm-bert/download-models.sh

  echo -e "${GREEN}RouteLL-BERT model downloaded successfully to $target_dir${NC}"
}

# Function to upload the model to cloud storage
upload_model() {
  local version=$1
  local source_dir="$MODELS_DIR/$version"

  echo -e "${GREEN}Uploading RouteLL-BERT model version $version to cloud storage...${NC}"

  # Check if the source directory exists
  if [ ! -d "$source_dir" ]; then
    echo -e "${RED}Error: Source directory $source_dir does not exist${NC}"
    exit 1
  fi

  # Set environment variables for the upload script
  export MODELS_DIR="$MODELS_DIR"
  export MODELS_PATH="$MODELS_PATH"
  export VERSION="$version"
  export CLOUD_PROVIDER="$CLOUD_PROVIDER"
  export AWS_REGION="$AWS_REGION"
  export BUCKET_NAME="$BUCKET_NAME"
  export AZURE_STORAGE_ACCOUNT="$AZURE_STORAGE_ACCOUNT"
  export AZURE_CONTAINER_NAME="$AZURE_CONTAINER_NAME"
  export GCP_PROJECT_ID="$GCP_PROJECT_ID"
  export GCP_BUCKET_NAME="$GCP_BUCKET_NAME"
  export GCP_REGION="$GCP_REGION"

  # Run the upload script
  ./routellm-bert/upload-models.sh

  echo -e "${GREEN}RouteLL-BERT model uploaded successfully${NC}"
}

# Function to upload the model directly to S3
upload_model_to_s3() {
  echo -e "${GREEN}Uploading RouteLL-BERT model directly to S3...${NC}"

  # Set environment variables for the upload_models_to_s3 script
  export AWS_ACCOUNT_ID="$AWS_ACCOUNT_ID"
  export AWS_REGION="$AWS_REGION"

  # Run the upload_models_to_s3 script
  ./routellm-bert/upload_models_to_s3.sh

  echo -e "${GREEN}RouteLL-BERT model uploaded to S3 successfully${NC}"
}

# Function to rollback to a previous version
rollback_model() {
  echo -e "${GREEN}Rolling back RouteLL-BERT model...${NC}"

  # Set environment variables for the rollback script
  export CLOUD_PROVIDER="$CLOUD_PROVIDER"
  export APP_NAME="$APP_NAME"
  export ENVIRONMENT="$ENVIRONMENT"
  export AWS_REGION="$AWS_REGION"
  export GCP_REGION="$GCP_REGION"

  # Run the rollback script
  ./routellm-bert/rollback.sh --cloud-provider "$CLOUD_PROVIDER" --app-name "$APP_NAME" --environment "$ENVIRONMENT"

  echo -e "${GREEN}RouteLL-BERT model rollback completed successfully${NC}"
}

# Execute the requested action
case "$ACTION" in
  setup)
    setup_model
    ;;
  download)
    download_model "$VERSION" "$FORCE_DOWNLOAD"
    ;;
  upload)
    if [ -z "$VERSION" ]; then
      echo -e "${RED}Error: Version (--version=x.x) is required for upload${NC}"
      exit 1
    fi
    upload_model "$VERSION"
    ;;
  upload-to-s3)
    upload_model_to_s3
    ;;
  rollback)
    rollback_model
    ;;
  *)
    echo -e "${RED}Error: Invalid action: $ACTION${NC}"
    usage
    ;;
esac

echo -e "${GREEN}RouteLL-BERT model management completed successfully${NC}"
