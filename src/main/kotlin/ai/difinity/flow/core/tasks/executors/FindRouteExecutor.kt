package ai.difinity.flow.core.tasks.executors

import ai.difinity.flow.core.chat.conf.models.ExecutionConfig
import ai.difinity.flow.core.llm.AIModelRouter
import ai.difinity.flow.core.llm.models.FlowQuery
import ai.difinity.flow.core.llm.models.LLMModel
import ai.difinity.flow.core.llm.models.LLMProvider
import ai.difinity.flow.core.llm.models.MODEL_AUTO_SELECT
import ai.difinity.flow.core.tasks.BaseFlowTaskExecutor
import ai.difinity.flow.core.tasks.FlowTaskResult
import org.springframework.stereotype.Component

@Component

class FindRouteExecutor(
    private val aiModelRouter: AIModelRouter,
) : BaseFlowTaskExecutor<FlowQuery, LLMModel>("findRoute") {
    override fun isApplicable(executionConfig: ExecutionConfig): Boolean = true

    override suspend fun execute(
        input: FlowQuery,
        executionConfig: ExecutionConfig,
    ): FlowTaskResult<LLMModel> {
        val aiModelToUse =
            if (input.model == MODEL_AUTO_SELECT) {
                aiModelRouter.route(
                    input,
                    executionConfig.enabledModels
                        .flatMap { e -> e.value.map { LLMModel(LLMProvider.valueOf(e.key.uppercase()), it) } },
                )
            } else {
                LLMModel(
                    provider = input.provider!!,
                    model = input.model,
                )
            }
        return FlowTaskResult.Success(taskType, aiModelToUse)
    }
}
