package ai.difinity.flow.core.tasks.executors

import ai.difinity.flow.core.chat.conf.models.ExecutionConfig
import ai.difinity.flow.core.chat.evaluation.EvaluationResult
import ai.difinity.flow.core.chat.evaluation.Evaluator
import ai.difinity.flow.core.llm.models.FlowQuery
import ai.difinity.flow.core.tasks.BaseFlowTaskExecutor
import ai.difinity.flow.core.tasks.FlowTaskResult
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component

@Component
@Qualifier("evaluationExecutor")
class EvaluationExecutor(
    @Qualifier("subjectiveEvaluator") private val evaluator: Evaluator,
) : BaseFlowTaskExecutor<FlowQuery, Any>("subjectiveEvaluation") {
    override fun isApplicable(executionConfig: ExecutionConfig): Boolean =
        executionConfig.harmfulContentCheck ||
            executionConfig.toxicContentCheck ||
            executionConfig.fakeNewsCheck ||
            executionConfig.phishingCheck ||
            executionConfig.insensitiveLanguageCheck ||
            executionConfig.factCheckingCheck

    suspend fun doExecute(
        input: FlowQuery,
        executionConfig: ExecutionConfig,
    ): EvaluationResult = evaluator.evaluate(input, executionConfig)

    override suspend fun execute(
        input: FlowQuery,
        executionConfig: ExecutionConfig,
    ): FlowTaskResult<Any> {
        return try {
            val result = doExecute(input, executionConfig)
            return if (result.success) {
                FlowTaskResult.Success(taskType, result)
            } else {
                FlowTaskResult.DomainFailure(taskType, result.reason.orEmpty(), result)
            }
        } catch (ex: Exception) {
            FlowTaskResult.SystemFailure(taskType, ex.message.orEmpty(), ex)
        }
    }
}
