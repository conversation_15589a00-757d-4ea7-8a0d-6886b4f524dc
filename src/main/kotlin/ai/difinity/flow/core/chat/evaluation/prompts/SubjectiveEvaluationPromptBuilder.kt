package ai.difinity.flow.core.chat.evaluation.prompts

import ai.difinity.flow.core.chat.conf.models.ExecutionConfig
import ai.difinity.flow.core.chat.evaluation.evaluators.SubjectiveCheck
import com.mitchellbosecke.pebble.PebbleEngine
import com.mitchellbosecke.pebble.loader.ClasspathLoader
import com.mitchellbosecke.pebble.loader.FileLoader
import org.springframework.stereotype.Component
import java.io.StringWriter
import java.nio.file.Files
import java.nio.file.Paths

/**
 * Builds system prompts for subjective evaluation based on enabled checks in ExecutionConfig
 */
@Component
class SubjectiveEvaluationPromptBuilder {
    // Map of PredefinedChecks to their corresponding template files
    private val checkToTemplateMap =
        mapOf(
            SubjectiveCheck.HARMFUL_CONTENT_CHECK to "harmful_content_check.pebble",
            SubjectiveCheck.TOXIC_CONTENT_CHECK to "toxic_content_check.pebble",
            SubjectiveCheck.FAKE_NEWS_CHECK to "fake_news_check.pebble",
            SubjectiveCheck.PHISHING_CHECK to "phishing_check.pebble",
            SubjectiveCheck.INSENSITIVE_LANGUAGE_CHECK to "insensitive_language_check.pebble",
            SubjectiveCheck.FACT_CHECKING_CHECK to "fact_checking_check.pebble",
        )

    // Initialize Pebble Engine
    private val pebbleEngine: PebbleEngine by lazy {
        // First try to load from classpath
        val classpathLoader = ClasspathLoader()
        classpathLoader.prefix = "ai/difinity/flow/core/chat/evaluation/prompts/templates/"

        // If templates directory exists on filesystem, use FileLoader instead
        val templatesPath = Paths.get("src/main/kotlin/ai/difinity/flow/core/chat/evaluation/prompts/templates")
        val loader =
            if (Files.exists(templatesPath)) {
                val fileLoader = FileLoader()
                fileLoader.prefix = templatesPath.toString()
                fileLoader
            } else {
                classpathLoader
            }

        PebbleEngine
            .Builder()
            .loader(loader)
            .build()
    }

    /**
     * Builds a system prompt for a specific check
     *
     * @param check The specific check to build a prompt for
     * @param executionConfig The execution configuration
     * @param prompt The user prompt to evaluate
     * @return A system prompt for the specific check
     */
    fun buildPromptForCheck(
        check: SubjectiveCheck,
        executionConfig: ExecutionConfig,
        prompt: String,
    ): String {
        return renderTemplate(check, executionConfig, prompt)
    }

    /**
     * Gets the list of enabled checks from ExecutionConfig
     */
    fun getEnabledChecks(executionConfig: ExecutionConfig): List<SubjectiveCheck> {
        val enabledChecks = mutableListOf<SubjectiveCheck>()

        if (executionConfig.harmfulContentCheck) {
            enabledChecks.add(SubjectiveCheck.HARMFUL_CONTENT_CHECK)
        }

        if (executionConfig.toxicContentCheck) {
            enabledChecks.add(SubjectiveCheck.TOXIC_CONTENT_CHECK)
        }

        if (executionConfig.fakeNewsCheck) {
            enabledChecks.add(SubjectiveCheck.FAKE_NEWS_CHECK)
        }

        if (executionConfig.phishingCheck) {
            enabledChecks.add(SubjectiveCheck.PHISHING_CHECK)
        }

        if (executionConfig.insensitiveLanguageCheck) {
            enabledChecks.add(SubjectiveCheck.INSENSITIVE_LANGUAGE_CHECK)
        }

        if (executionConfig.factCheckingCheck) {
            enabledChecks.add(SubjectiveCheck.FACT_CHECKING_CHECK)
        }

        return enabledChecks
    }

    /**
     * Renders a template for a specific check
     */
    fun renderTemplate(
        check: SubjectiveCheck,
        executionConfig: ExecutionConfig,
        prompt: String,
    ): String {
        val templateName = checkToTemplateMap[check] ?: throw IllegalArgumentException("No template found for check: $check")

        val template = pebbleEngine.getTemplate(templateName)
        val writer = StringWriter()

        // Prepare the context with variables for the template
        val context = mutableMapOf<String, Any>()
        context["prompt"] = prompt

        // Add check-specific variables
        when (check) {
            SubjectiveCheck.FACT_CHECKING_CHECK -> {
                context["sources"] = executionConfig.factCheckingSource
            }
            else -> {
                // No additional variables needed for other checks
            }
        }

        // Render the template
        template.evaluate(writer, context)
        return writer.toString()
    }
}
