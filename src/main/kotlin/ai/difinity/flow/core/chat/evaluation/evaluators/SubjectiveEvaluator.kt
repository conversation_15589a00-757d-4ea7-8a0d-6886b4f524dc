package ai.difinity.flow.core.chat.evaluation.evaluators

import ai.difinity.flow.adapters.mistral.MistralLLMClient
import ai.difinity.flow.core.chat.conf.models.ExecutionConfig
import ai.difinity.flow.core.chat.evaluation.EvaluationResult
import ai.difinity.flow.core.chat.evaluation.Evaluator
import ai.difinity.flow.core.chat.evaluation.prompts.SubjectiveEvaluationPromptBuilder
import ai.difinity.flow.core.llm.AIEvaluationService
import ai.difinity.flow.core.llm.models.FlowMessage
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * Data class representing the JSON response format expected from subjective evaluation templates
 */
data class SubjectiveCheckResult(
    @JsonProperty("check_name")
    val checkName: String,
    @JsonProperty("passed")
    val passed: Boolean,
    @JsonProperty("probability")
    val probability: String,
    @JsonProperty("reason")
    val reason: String
)

@Component
class SubjectiveEvaluator(
    private val promptBuilder: SubjectiveEvaluationPromptBuilder,
    private val aiEvaluationService: AIEvaluationService
) : Evaluator {

    private val logger = LoggerFactory.getLogger(SubjectiveEvaluator::class.java)

    override fun isApplicable(executionConfig: ExecutionConfig): Boolean {
        return executionConfig.harmfulContentCheck ||
                executionConfig.toxicContentCheck ||
                executionConfig.fakeNewsCheck ||
                executionConfig.phishingCheck ||
                executionConfig.insensitiveLanguageCheck ||
                executionConfig.factCheckingCheck
    }

    override suspend fun evaluate(
        message: FlowMessage,
        executionConfig: ExecutionConfig,
        historyMessages: List<FlowMessage>,
    ): EvaluationResult {
        try {
            // Get enabled checks from configuration
            val enabledChecks = getEnabledChecks(executionConfig)

            if (enabledChecks.isEmpty()) {
                logger.debug("No subjective checks enabled, returning success")
                return EvaluationResult(this::class.java, true, "No subjective checks enabled")
            }

            // Combine message with history to get full content to evaluate
            val fullContent = buildFullContent(message, historyMessages)

            // Evaluate each enabled check
            val checkResults = mutableListOf<SubjectiveCheckResult>()
            val failedChecks = mutableListOf<String>()

            for (check in enabledChecks) {
                try {
                    val result = evaluateIndividualCheck(check, fullContent, executionConfig)
                    checkResults.add(result)

                    if (!result.passed) {
                        failedChecks.add("${result.checkName}: ${result.reason}")
                    }
                } catch (e: Exception) {
                    logger.error("Failed to evaluate check $check", e)
                    failedChecks.add("$check: Evaluation failed due to system error")
                }
            }

            // Determine overall result
            val overallSuccess = failedChecks.isEmpty()
            val reason = if (overallSuccess) {
                "All subjective checks passed"
            } else {
                "Failed checks: ${failedChecks.joinToString("; ")}"
            }

            logger.debug("Subjective evaluation completed. Success: $overallSuccess, Reason: $reason")
            return EvaluationResult(this::class.java, overallSuccess, reason)

        } catch (e: Exception) {
            logger.error("Subjective evaluation failed with system error", e)
            return EvaluationResult(this::class.java, false, "System error during evaluation: ${e.message}")
        }
    }

    /**
     * Builds the full content to evaluate by combining the current message with history
     */
    private fun buildFullContent(message: FlowMessage, historyMessages: List<FlowMessage>): String {
        // For subjective evaluation, we typically want to evaluate the current message
        // but we could include recent history for context if needed
        return message.content
    }

    /**
     * Gets the list of enabled checks from ExecutionConfig
     */
    private fun getEnabledChecks(executionConfig: ExecutionConfig): List<SubjectiveCheck> {
        val enabledChecks = mutableListOf<SubjectiveCheck>()

        if (executionConfig.harmfulContentCheck) {
            enabledChecks.add(SubjectiveCheck.HARMFUL_CONTENT_CHECK)
        }

        if (executionConfig.toxicContentCheck) {
            enabledChecks.add(SubjectiveCheck.TOXIC_CONTENT_CHECK)
        }

        if (executionConfig.fakeNewsCheck) {
            enabledChecks.add(SubjectiveCheck.FAKE_NEWS_CHECK)
        }

        if (executionConfig.phishingCheck) {
            enabledChecks.add(SubjectiveCheck.PHISHING_CHECK)
        }

        if (executionConfig.insensitiveLanguageCheck) {
            enabledChecks.add(SubjectiveCheck.INSENSITIVE_LANGUAGE_CHECK)
        }

        if (executionConfig.factCheckingCheck) {
            enabledChecks.add(SubjectiveCheck.FACT_CHECKING_CHECK)
        }

        return enabledChecks
    }

    /**
     * Evaluates an individual check using the AI Evaluation Service
     */
    private suspend fun evaluateIndividualCheck(
        check: SubjectiveCheck,
        content: String,
        executionConfig: ExecutionConfig
    ): SubjectiveCheckResult {
        // Build the prompt for this specific check
        val systemPrompt = promptBuilder.buildPromptForCheck(check, executionConfig, content)

        // Use empty user message since the content is already in the system prompt
        val userMessage = ""

        logger.debug("Evaluating check $check with system prompt length: ${systemPrompt.length}")

        // Call AI Evaluation Service for JSON completion
        val response = aiEvaluationService.evaluateWithStructuredOutput(
            systemPrompt = systemPrompt,
            userMessage = userMessage,
            outputClass = SubjectiveCheckResult::class.java,
            temperature = 0.1, // Low temperature for consistent evaluation
            topP = 0.9,
            maxTokens = 512 // Should be enough for the JSON response
        )

        logger.debug("Check $check completed. Passed: ${response.passed}, Reason: ${response.reason}")
        return response
    }
}
