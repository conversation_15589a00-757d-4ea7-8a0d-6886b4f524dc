package ai.difinity.flow.core.llm

/**
 * Service interface for AI-based evaluation operations
 * This interface provides methods for making structured AI calls for evaluation purposes
 */
interface AIEvaluationService {

    /**
     * Performs a structured JSON evaluation using an AI model
     *
     * @param systemPrompt The system prompt to guide the AI's behavior
     * @param userMessage The user message/content to evaluate (can be empty if content is in system prompt)
     * @param outputClass The class that defines the expected JSON structure
     * @param temperature Controls randomness (0.0 to 1.0), default 0.1 for consistent evaluation
     * @param topP Controls diversity via nucleus sampling (0.0 to 1.0), default 0.9
     * @param maxTokens Maximum number of tokens to generate, default 512
     * @return The parsed JSON response of type T
     */
    suspend fun <T> evaluateWithStructuredOutput(
        systemPrompt: String,
        userMessage: String = "",
        outputClass: Class<T>,
        temperature: Double = 0.1,
        topP: Double = 0.9,
        maxTokens: Int = 512
    ): T
}
