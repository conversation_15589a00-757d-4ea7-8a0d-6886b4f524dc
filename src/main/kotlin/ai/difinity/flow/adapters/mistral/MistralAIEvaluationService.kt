package ai.difinity.flow.adapters.mistral

import ai.difinity.flow.core.llm.AIEvaluationService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * Mistral-based implementation of AIEvaluationService
 * This service uses the MistralService for evaluation operations
 */
@Service
class MistralAIEvaluationService(
    private val mistralService: MistralService
) : AIEvaluationService {
    
    private val logger = LoggerFactory.getLogger(MistralAIEvaluationService::class.java)
    
    override suspend fun <T> evaluateWithStructuredOutput(
        systemPrompt: String,
        userMessage: String,
        outputClass: Class<T>,
        temperature: Double,
        topP: Double,
        maxTokens: Int
    ): T {
        logger.debug("Performing structured evaluation with Mistral AI. Output class: ${outputClass.simpleName}")
        
        try {
            return mistralService.generateJsonResponse(
                systemPrompt = systemPrompt,
                userMessage = userMessage,
                outputClass = outputClass,
                temperature = temperature,
                topP = topP,
                maxTokens = maxTokens
            )
        } catch (e: Exception) {
            logger.error("Error during Mistral AI evaluation", e)
            throw e
        }
    }
}
